@using CarInsuranceWebsite.ViewModels
@using CarInsuranceWebsite.Models
@model CreateUserViewModel
@{
    ViewData["Title"] = "إضافة مستخدم جديد";
    var companies = ViewBag.Companies as List<InsuranceCompany>;
}

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">إضافة مستخدم جديد</h1>
        <a href="@Url.Action("Users")" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            العودة للقائمة
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">بيانات المستخدم</h6>
                </div>
                <div class="card-body">
                    <form asp-action="CreateUser" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <!-- Personal Information -->
                        <h6 class="text-primary mb-3">المعلومات الشخصية</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="FirstName" class="form-label"></label>
                                <input asp-for="FirstName" class="form-control" />
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="LastName" class="form-label"></label>
                                <input asp-for="LastName" class="form-control" />
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="NationalId" class="form-label"></label>
                                <input asp-for="NationalId" class="form-control" placeholder="123456789012" maxlength="12" />
                                <span asp-validation-for="NationalId" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="DateOfBirth" class="form-label"></label>
                                <input asp-for="DateOfBirth" class="form-control" type="date" />
                                <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <h6 class="text-primary mb-3 mt-4">معلومات الاتصال</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label"></label>
                                <input asp-for="Email" class="form-control" placeholder="<EMAIL>" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="PhoneNumber" class="form-label"></label>
                                <input asp-for="PhoneNumber" class="form-control" placeholder="+218912345678" />
                                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label"></label>
                            <input asp-for="Address" class="form-control" />
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <!-- Account Information -->
                        <h6 class="text-primary mb-3 mt-4">معلومات الحساب</h6>

                        <!-- Password Requirements Info -->
                        <div class="alert alert-info mb-3">
                            <h6 class="alert-heading mb-2">
                                <i class="fas fa-info-circle me-2"></i>
                                متطلبات كلمة المرور:
                            </h6>
                            <ul class="mb-0 small">
                                <li>يجب أن تكون على الأقل 6 أحرف</li>
                                <li>يمكن استخدام أي أحرف أو أرقام أو رموز</li>
                                <li>مثال: <code>123456</code> أو <code>password</code> أو <code>Admin@123</code></li>
                            </ul>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Password" class="form-label"></label>
                                <input asp-for="Password" class="form-control" type="password" placeholder="أدخل كلمة المرور (6 أحرف على الأقل)" />
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label asp-for="ConfirmPassword" class="form-label"></label>
                                <input asp-for="ConfirmPassword" class="form-control" type="password" placeholder="أعد كتابة كلمة المرور" />
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Role" class="form-label"></label>
                                <select asp-for="Role" class="form-select" id="roleSelect" required>
                                    <option value="">اختر الدور</option>
                                    <option value="SystemAdmin">مدير النظام</option>
                                    <option value="CompanyManager">مدير شركة</option>
                                </select>
                                <span asp-validation-for="Role" class="text-danger"></span>
                                <div class="form-text">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        مدير النظام: يدير النظام بالكامل | مدير شركة: يدير شركة تأمين واحدة
                                    </small>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3" id="companyDiv" style="display: none;">
                                <label asp-for="InsuranceCompanyId" class="form-label"></label>
                                <select asp-for="InsuranceCompanyId" class="form-select">
                                    <option value="">اختر الشركة</option>
                                    @if (companies != null)
                                    {
                                        @foreach (var company in companies)
                                        {
                                            <option value="@company.Id">@company.Name</option>
                                        }
                                    }
                                </select>
                                <span asp-validation-for="InsuranceCompanyId" class="text-danger"></span>
                                <div class="form-text">
                                    <small class="text-muted">
                                        <i class="fas fa-building me-1"></i>
                                        اختر الشركة التي سيديرها هذا المستخدم
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="@Url.Action("Users")" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ المستخدم
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        document.getElementById('roleSelect').addEventListener('change', function() {
            var companyDiv = document.getElementById('companyDiv');
            var companySelect = document.querySelector('#companyDiv select');

            if (this.value === 'CompanyManager') {
                companyDiv.style.display = 'block';
                companySelect.setAttribute('required', 'required');
            } else {
                companyDiv.style.display = 'none';
                companySelect.removeAttribute('required');
                companySelect.value = '';
            }
        });

        // Password strength indicator
        document.querySelector('input[name="Password"]').addEventListener('input', function() {
            var password = this.value;
            var strengthDiv = document.getElementById('passwordStrength');

            if (!strengthDiv) {
                strengthDiv = document.createElement('div');
                strengthDiv.id = 'passwordStrength';
                strengthDiv.className = 'mt-2';
                this.parentNode.appendChild(strengthDiv);
            }

            if (password.length === 0) {
                strengthDiv.innerHTML = '';
                return;
            }

            var strength = '';
            var className = '';

            if (password.length < 6) {
                strength = 'ضعيفة جداً - يجب 6 أحرف على الأقل';
                className = 'text-danger';
            } else if (password.length < 8) {
                strength = 'مقبولة';
                className = 'text-warning';
            } else {
                strength = 'قوية';
                className = 'text-success';
            }

            strengthDiv.innerHTML = '<small class="' + className + '"><i class="fas fa-shield-alt me-1"></i>قوة كلمة المرور: ' + strength + '</small>';
        });

        // Form validation before submit
        document.querySelector('form').addEventListener('submit', function(e) {
            var role = document.getElementById('roleSelect').value;
            var company = document.querySelector('#companyDiv select').value;

            if (role === 'CompanyManager' && !company) {
                e.preventDefault();
                alert('يرجى اختيار الشركة لمدير الشركة');
                return false;
            }
        });
    </script>
}
