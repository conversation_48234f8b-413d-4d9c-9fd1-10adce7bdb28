using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using CarInsuranceWebsite.Models;
using CarInsuranceWebsite.ViewModels;
using CarInsuranceWebsite.Data;

namespace CarInsuranceWebsite.Controllers
{
    public class AccountController : Controller
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;

        public AccountController(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager)
        {
            _userManager = userManager;
            _signInManager = signInManager;
        }

        [HttpGet]
        public IActionResult Login(string? returnUrl = null)
        {
            ViewData["ReturnUrl"] = returnUrl;
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(LoginViewModel model, string? returnUrl = null)
        {
            ViewData["ReturnUrl"] = returnUrl;
            
            if (ModelState.IsValid)
            {
                var result = await _signInManager.PasswordSignInAsync(model.Email, model.Password, model.RememberMe, lockoutOnFailure: false);
                
                if (result.Succeeded)
                {
                    var user = await _userManager.FindByEmailAsync(model.Email);
                    if (user != null)
                    {
                        var roles = await _userManager.GetRolesAsync(user);
                        
                        // Redirect based on user role
                        if (roles.Contains("SystemAdmin"))
                        {
                            return RedirectToAction("Index", "SystemAdmin");
                        }
                        else if (roles.Contains("CompanyManager"))
                        {
                            return RedirectToAction("Index", "CompanyManager");
                        }
                        else
                        {
                            return RedirectToLocal(returnUrl);
                        }
                    }
                }
                
                ModelState.AddModelError(string.Empty, "محاولة تسجيل دخول غير صحيحة.");
            }

            return View(model);
        }

        // Registration is disabled for public users
        // Only SystemAdmin can create new users through admin panel

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Logout()
        {
            await _signInManager.SignOutAsync();
            return RedirectToAction("Index", "Home");
        }

        private IActionResult RedirectToLocal(string? returnUrl)
        {
            if (Url.IsLocalUrl(returnUrl))
            {
                return Redirect(returnUrl);
            }
            else
            {
                return RedirectToAction("Index", "Home");
            }
        }
    }
}
