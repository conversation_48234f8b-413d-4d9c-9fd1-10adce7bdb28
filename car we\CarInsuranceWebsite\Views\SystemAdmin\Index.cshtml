@using CarInsuranceWebsite.ViewModels
@model SystemAdminDashboardViewModel
@{
    ViewData["Title"] = "لوحة تحكم مدير النظام";
}

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">لوحة تحكم مدير النظام</h1>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Total Companies Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الشركات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalCompanies</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Companies Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                الشركات النشطة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.ActiveCompanies</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Users Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                إجمالي المستخدمين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalUsers</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Company Managers Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                مديري الشركات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.CompanyManagers.Count</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Quick Actions -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الإجراءات السريعة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <a href="@Url.Action("CreateCompany", "SystemAdmin")" class="btn btn-primary btn-block">
                                <i class="fas fa-plus me-2"></i>
                                إضافة شركة جديدة
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="@Url.Action("CreateUser", "SystemAdmin")" class="btn btn-success btn-block">
                                <i class="fas fa-user-plus me-2"></i>
                                إضافة مستخدم جديد
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="@Url.Action("Companies", "SystemAdmin")" class="btn btn-info btn-block">
                                <i class="fas fa-building me-2"></i>
                                إدارة الشركات
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="@Url.Action("Users", "SystemAdmin")" class="btn btn-warning btn-block">
                                <i class="fas fa-users me-2"></i>
                                إدارة المستخدمين
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Companies -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الشركات الحديثة</h6>
                </div>
                <div class="card-body">
                    @if (Model.RecentCompanies.Any())
                    {
                        @foreach (var company in Model.RecentCompanies)
                        {
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    <div class="icon-circle @(company.IsActive ? "bg-success" : "bg-secondary")">
                                        <i class="fas fa-building text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="small text-gray-500">@company.Name</div>
                                    <div class="font-weight-bold">
                                        @(company.IsActive ? "نشط" : "غير نشط")
                                    </div>
                                </div>
                                <div>
                                    <a href="@Url.Action("EditCompany", "SystemAdmin", new { id = company.Id })" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <p class="text-muted text-center">لا توجد شركات مسجلة بعد</p>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
</style>
