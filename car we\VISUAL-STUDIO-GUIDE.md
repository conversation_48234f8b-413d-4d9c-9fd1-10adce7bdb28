# 🎯 دليل فتح المشروع في Visual Studio 2022

## 📋 المتطلبات

تأكد من تثبيت:
- ✅ **Visual Studio 2022** (Community, Professional, أو Enterprise)
- ✅ **ASP.NET and web development workload**
- ✅ **.NET 9.0 SDK**

---

## 🚀 خطوات فتح المشروع

### الطريقة 1: فتح Solution File (الأفضل)

1. **افتح Visual Studio 2022**
2. **اختر "Open a project or solution"**
3. **انتقل إلى مجلد المشروع**
4. **اختر ملف `CarInsuranceWebsite.sln`**
5. **اضغط "Open"**

### الطريقة 2: فتح المجلد مباشرة

1. **افتح Visual Studio 2022**
2. **اختر "Open a local folder"**
3. **اختر مجلد `CarInsuranceWebsite`**
4. **انتظر حتى يتم تحميل المشروع**

---

## ⚙️ إعداد المشروع

### 1. استعادة Packages

عند فتح المشروع لأول مرة:
- Visual Studio سيقوم تلقائياً بـ restore packages
- إذا لم يحدث، اذهب إلى **Tools > NuGet Package Manager > Package Manager Console**
- اكتب: `dotnet restore`

### 2. إعداد قاعدة البيانات

في **Package Manager Console** اكتب:
```powershell
Update-Database
```

### 3. اختيار Startup Profile

في شريط الأدوات العلوي، ستجد قائمة منسدلة بجانب زر التشغيل:
- **CarInsuranceWebsite** - للتشغيل العادي (localhost)
- **IIS Express** - للتشغيل مع IIS
- **Network Access** - للوصول من أجهزة أخرى

---

## ▶️ تشغيل المشروع

### للتشغيل العادي:
1. **اختر "CarInsuranceWebsite" من القائمة المنسدلة**
2. **اضغط F5 أو الزر الأخضر "Start"**
3. **سيفتح المتصفح تلقائياً على `https://localhost:7194`**

### للوصول من أجهزة أخرى:
1. **اختر "Network Access" من القائمة المنسدلة**
2. **اضغط F5**
3. **استخدم عنوان IP الخاص بك مع Port 5165**
4. **مثال: `http://*************:5165`**

---

## 🔧 أدوات مفيدة في Visual Studio

### 1. Package Manager Console
- **Tools > NuGet Package Manager > Package Manager Console**
- لتنفيذ أوامر Entity Framework والـ NuGet

### 2. SQL Server Object Explorer
- **View > SQL Server Object Explorer**
- لاستعراض قاعدة البيانات

### 3. Solution Explorer
- **View > Solution Explorer**
- لاستعراض ملفات المشروع

### 4. Error List
- **View > Error List**
- لرؤية الأخطاء والتحذيرات

---

## 🐛 حل المشاكل الشائعة

### مشكلة: "The project doesn't know how to run the profile"
**الحل:**
1. انقر بزر الماوس الأيمن على المشروع
2. اختر "Set as Startup Project"

### مشكلة: "Unable to connect to web server"
**الحل:**
1. تأكد من أن Port 5165 و 7194 غير مستخدمين
2. شغل Visual Studio كـ Administrator
3. أعد تشغيل Visual Studio

### مشكلة: قاعدة البيانات لا تعمل
**الحل:**
```powershell
# في Package Manager Console
Drop-Database
Update-Database
```

### مشكلة: Packages مفقودة
**الحل:**
```powershell
# في Package Manager Console
dotnet restore
dotnet build
```

---

## 🔐 بيانات تسجيل الدخول

### مدير النظام (السوبر أدمن)
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `Admin@123`

### مدير الشركة (للاختبار)
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `Manager@123`

---

## 📱 اختبار الموقع

بعد التشغيل، جرب:
1. **الصفحة الرئيسية** - تصفح الشركات والمكاتب
2. **تسجيل الدخول** - جرب بيانات المدير
3. **لوحة التحكم** - إدارة الشركات والمكاتب
4. **الاستجابة** - جرب على أحجام شاشة مختلفة

---

## 🎨 تخصيص المشروع

### تغيير الألوان:
- **wwwroot/css/site.css** - الألوان الأساسية
- **Views/Shared/_Layout.cshtml** - تخطيط الصفحة

### إضافة صفحات جديدة:
1. انقر بزر الماوس الأيمن على **Controllers**
2. اختر **Add > Controller**
3. اختر **MVC Controller - Empty**

### تعديل قاعدة البيانات:
1. عدل الـ Models في مجلد **Models**
2. في Package Manager Console:
```powershell
Add-Migration YourMigrationName
Update-Database
```

---

## 🌟 نصائح للتطوير

1. **استخدم Hot Reload** - تغييرات CSS/HTML تظهر فوراً
2. **استخدم Breakpoints** - لتتبع الأخطاء
3. **استخدم IntelliSense** - للكتابة السريعة
4. **استخدم Git** - لحفظ التغييرات

---

**🎉 مبروك! المشروع جاهز للعمل في Visual Studio 2022**
