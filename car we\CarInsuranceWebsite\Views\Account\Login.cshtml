@model CarInsuranceWebsite.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "تسجيل الدخول";
}

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm border-0">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="h3 mb-3">تسجيل الدخول</h2>
                        <p class="text-muted">أدخل بياناتك للوصول إلى حسابك</p>
                    </div>

                    <form asp-action="Login" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        <input type="hidden" asp-for="ReturnUrl" />

                        <div class="mb-3">
                            <label asp-for="Email" class="form-label"></label>
                            <input asp-for="Email" class="form-control form-control-lg" placeholder="أدخل البريد الإلكتروني" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label"></label>
                            <input asp-for="Password" class="form-control form-control-lg" placeholder="أدخل كلمة المرور" />
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="form-check mb-4">
                            <input asp-for="RememberMe" class="form-check-input" />
                            <label asp-for="RememberMe" class="form-check-label"></label>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="mb-0">ليس لديك حساب؟
                            <a asp-action="Register" class="text-decoration-none">إنشاء حساب جديد</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
