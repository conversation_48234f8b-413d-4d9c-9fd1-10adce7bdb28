using CarInsuranceWebsite.Models;

namespace CarInsuranceWebsite.ViewModels
{
    public class CompanySearchViewModel
    {
        public string? SearchTerm { get; set; }
        public List<InsuranceCompany> Companies { get; set; } = new List<InsuranceCompany>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public int CurrentPage => PageNumber;
    }
}
