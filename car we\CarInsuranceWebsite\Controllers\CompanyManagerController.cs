using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using CarInsuranceWebsite.Data;
using CarInsuranceWebsite.Models;
using CarInsuranceWebsite.ViewModels;

namespace CarInsuranceWebsite.Controllers
{
    [Authorize(Roles = "CompanyManager")]
    public class CompanyManagerController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public CompanyManagerController(ApplicationDbContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public async Task<IActionResult> Index()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.InsuranceCompanyId == null)
            {
                // User is not assigned to a company yet
                ViewBag.Message = "لم يتم تعيينك لشركة تأمين بعد. يرجى الاتصال بمدير النظام.";
                return View("NoCompany");
            }

            var company = await _context.InsuranceCompanies
                .Include(c => c.Offices)
                .ThenInclude(o => o.Offers)
                .FirstOrDefaultAsync(c => c.Id == user.InsuranceCompanyId);

            if (company == null)
            {
                ViewBag.Message = "الشركة غير موجودة. يرجى الاتصال بمدير النظام.";
                return View("NoCompany");
            }

            var viewModel = new CompanyManagerDashboardViewModel
            {
                Company = company,
                TotalOffices = company.Offices.Count,
                ActiveOffices = company.Offices.Count(o => o.IsActive),
                TotalOffers = company.Offices.SelectMany(o => o.Offers).Count(),
                RecentOffices = company.Offices.OrderByDescending(o => o.CreatedAt).Take(5).ToList()
            };

            return View(viewModel);
        }

        // Offices Management
        public async Task<IActionResult> Offices()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.InsuranceCompanyId == null)
            {
                return RedirectToAction("Index");
            }

            var offices = await _context.CompanyOffices
                .Include(o => o.Offers)
                .Where(o => o.InsuranceCompanyId == user.InsuranceCompanyId)
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();

            return View(offices);
        }

        [HttpGet]
        public async Task<IActionResult> CreateOffice()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.InsuranceCompanyId == null)
            {
                return RedirectToAction("Index");
            }

            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateOffice(CreateOfficeViewModel model)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.InsuranceCompanyId == null)
            {
                return RedirectToAction("Index");
            }

            if (ModelState.IsValid)
            {
                var office = new CompanyOffice
                {
                    Name = model.Name,
                    Address = model.Address,
                    PhoneNumber = model.PhoneNumber,
                    Description = model.Description,
                    WorkingHours = model.WorkingHours,
                    InsuranceCompanyId = user.InsuranceCompanyId.Value,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };

                _context.CompanyOffices.Add(office);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "تم إنشاء المكتب بنجاح";
                return RedirectToAction("Offices");
            }

            return View(model);
        }

        [HttpGet]
        public async Task<IActionResult> EditOffice(int id)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.InsuranceCompanyId == null)
            {
                return RedirectToAction("Index");
            }

            var office = await _context.CompanyOffices
                .FirstOrDefaultAsync(o => o.Id == id && o.InsuranceCompanyId == user.InsuranceCompanyId);

            if (office == null)
            {
                return NotFound();
            }

            var viewModel = new EditOfficeViewModel
            {
                Id = office.Id,
                Name = office.Name,
                Address = office.Address,
                PhoneNumber = office.PhoneNumber,
                Description = office.Description,
                WorkingHours = office.WorkingHours,
                IsActive = office.IsActive
            };

            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditOffice(EditOfficeViewModel model)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.InsuranceCompanyId == null)
            {
                return RedirectToAction("Index");
            }

            if (ModelState.IsValid)
            {
                var office = await _context.CompanyOffices
                    .FirstOrDefaultAsync(o => o.Id == model.Id && o.InsuranceCompanyId == user.InsuranceCompanyId);

                if (office == null)
                {
                    return NotFound();
                }

                office.Name = model.Name;
                office.Address = model.Address;
                office.PhoneNumber = model.PhoneNumber;
                office.Description = model.Description;
                office.WorkingHours = model.WorkingHours;
                office.IsActive = model.IsActive;

                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "تم تحديث المكتب بنجاح";
                return RedirectToAction("Offices");
            }

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteOffice(int id)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.InsuranceCompanyId == null)
            {
                return RedirectToAction("Index");
            }

            var office = await _context.CompanyOffices
                .FirstOrDefaultAsync(o => o.Id == id && o.InsuranceCompanyId == user.InsuranceCompanyId);

            if (office == null)
            {
                return NotFound();
            }

            _context.CompanyOffices.Remove(office);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "تم حذف المكتب بنجاح";
            return RedirectToAction("Offices");
        }

        // Insurance Offers Management
        public async Task<IActionResult> Offers(int? officeId)
        {
            var user = await _userManager.GetUserAsync(User);
            if (user?.InsuranceCompanyId == null)
            {
                return RedirectToAction("Index");
            }

            var query = _context.InsuranceOffers
                .Include(o => o.CompanyOffice)
                .Where(o => o.CompanyOffice.InsuranceCompanyId == user.InsuranceCompanyId);

            if (officeId.HasValue)
            {
                query = query.Where(o => o.CompanyOfficeId == officeId.Value);
            }

            var offers = await query.OrderByDescending(o => o.Id).ToListAsync();

            ViewBag.Offices = await _context.CompanyOffices
                .Where(o => o.InsuranceCompanyId == user.InsuranceCompanyId && o.IsActive)
                .ToListAsync();

            ViewBag.SelectedOfficeId = officeId;

            return View(offers);
        }
    }
}
