# 🎯 كيفية فتح المشروع في Visual Studio 2022

## 📂 ملفات المشروع الجاهزة:

✅ **CarInsuranceWebsite.sln** - ملف Solution الرئيسي  
✅ **CarInsuranceWebsite.csproj** - ملف المشروع  
✅ **launchSettings.json** - إعدادات التشغيل  
✅ **جميع الـ Packages** - مثبتة ومجهزة  

---

## 🚀 خطوات فتح المشروع:

### الطريقة الأولى (الأفضل):
1. **افتح Visual Studio 2022**
2. **اختر "Open a project or solution"**
3. **انتقل إلى مجلد:** `c:\Users\<USER>\OneDrive\Desktop\car we\`
4. **اختر ملف:** `CarInsuranceWebsite.sln`
5. **اضغط "Open"**

### الطريقة الثانية:
1. **افتح File Explorer**
2. **انتقل إلى:** `c:\Users\<USER>\OneDrive\Desktop\car we\`
3. **اضغط دبل كليك على:** `CarInsuranceWebsite.sln`
4. **سيفتح Visual Studio تلقائياً**

---

## ▶️ تشغيل المشروع:

### للتشغيل العادي (localhost):
1. **في شريط الأدوات العلوي، اختر "CarInsuranceWebsite"**
2. **اضغط F5 أو الزر الأخضر "Start"**
3. **سيفتح المتصفح على:** `https://localhost:7194`

### للوصول من أجهزة أخرى:
1. **اختر "Network Access" من القائمة المنسدلة**
2. **اضغط F5**
3. **استخدم عنوان IP مع Port 5165**

---

## 🔧 إعداد قاعدة البيانات:

### في Package Manager Console:
```powershell
# فتح Package Manager Console
Tools > NuGet Package Manager > Package Manager Console

# تحديث قاعدة البيانات
Update-Database
```

---

## 🔐 بيانات تسجيل الدخول المؤكدة:

### مدير النظام (السوبر أدمن):
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `Admin@123`

### مدير الشركة (للاختبار):
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `Manager@123`

---

## 🛠️ أدوات مفيدة:

### Solution Explorer:
- **View > Solution Explorer**
- لاستعراض ملفات المشروع

### Package Manager Console:
- **Tools > NuGet Package Manager > Package Manager Console**
- لتنفيذ أوامر Entity Framework

### Error List:
- **View > Error List**
- لرؤية الأخطاء والتحذيرات

---

## 🐛 حل المشاكل:

### إذا لم يعمل المشروع:
1. **انقر بزر الماوس الأيمن على المشروع**
2. **اختر "Set as Startup Project"**
3. **اضغط F5 مرة أخرى**

### إذا كانت قاعدة البيانات لا تعمل:
```powershell
# في Package Manager Console
Drop-Database
Update-Database
```

### إذا كانت Packages مفقودة:
```powershell
# في Package Manager Console
dotnet restore
dotnet build
```

---

## 🎉 المشروع جاهز!

بعد فتح المشروع في Visual Studio 2022:
- ✅ جميع الملفات موجودة
- ✅ جميع الـ Packages مثبتة
- ✅ قاعدة البيانات جاهزة
- ✅ إعدادات التشغيل محضرة
- ✅ واجهة عربية كاملة
- ✅ نظام صلاحيات متكامل

**استمتع بالتطوير! 🚀**
