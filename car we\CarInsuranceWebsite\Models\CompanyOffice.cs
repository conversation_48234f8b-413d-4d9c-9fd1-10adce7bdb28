using System.ComponentModel.DataAnnotations;

namespace CarInsuranceWebsite.Models
{
    public class CompanyOffice
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Address { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string PhoneNumber { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [StringLength(100)]
        public string WorkingHours { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        // Foreign key
        public int InsuranceCompanyId { get; set; }

        // Navigation properties
        public virtual InsuranceCompany InsuranceCompany { get; set; } = null!;
        public virtual ICollection<InsuranceOffer> Offers { get; set; } = new List<InsuranceOffer>();
    }
}
