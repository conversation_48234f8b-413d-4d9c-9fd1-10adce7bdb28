﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - تأمين السيارات</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/CarInsuranceWebsite.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
</head>
<body class="d-flex flex-column min-vh-100">
    <header>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand fw-bold" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-car me-2"></i>
                    تأمين السيارات
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">شركات التأمين</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="About">من نحن</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Contact">اتصل بنا</a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        @if (User.Identity.IsAuthenticated)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user me-1"></i>
                                    @User.Identity.Name
                                </a>
                                <ul class="dropdown-menu">
                                    @if (User.IsInRole("SystemAdmin"))
                                    {
                                        <li><a class="dropdown-item" asp-controller="SystemAdmin" asp-action="Index">لوحة تحكم النظام</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                    }
                                    else if (User.IsInRole("CompanyManager"))
                                    {
                                        <li><a class="dropdown-item" asp-controller="CompanyManager" asp-action="Index">لوحة تحكم الشركة</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                    }
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post" class="d-inline">
                                            <button type="submit" class="dropdown-item">تسجيل الخروج</button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Account" asp-action="Login">تسجيل الدخول</a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <main role="main" class="flex-grow-1">
        @RenderBody()
    </main>

    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>تأمين السيارات</h5>
                    <p class="text-muted">نقدم أفضل خدمات التأمين للسيارات في ليبيا</p>
                </div>
                <div class="col-md-4">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="@Url.Action("Index", "Home")" class="text-light text-decoration-none">الرئيسية</a></li>
                        <li><a href="@Url.Action("GetQuote", "Home")" class="text-light text-decoration-none">عرض سعر</a></li>
                        <li><a href="@Url.Action("Services", "Home")" class="text-light text-decoration-none">خدماتنا</a></li>
                        <li><a href="@Url.Action("Contact", "Home")" class="text-light text-decoration-none">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>تواصل معنا</h5>
                    <p class="text-muted">
                        <i class="fas fa-phone me-2"></i>061-2345678<br>
                        <i class="fas fa-envelope me-2"></i><EMAIL><br>
                        <i class="fas fa-map-marker-alt me-2"></i>بنغازي، ليبيا
                    </p>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0">&copy; 2025 - تأمين السيارات. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
