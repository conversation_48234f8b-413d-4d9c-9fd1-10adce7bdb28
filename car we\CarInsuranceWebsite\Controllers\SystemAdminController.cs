using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using CarInsuranceWebsite.Data;
using CarInsuranceWebsite.Models;
using CarInsuranceWebsite.ViewModels;

namespace CarInsuranceWebsite.Controllers
{
    [Authorize(Roles = "SystemAdmin")]
    public class SystemAdminController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;

        public SystemAdminController(ApplicationDbContext context, UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userManager = userManager;
        }

        public async Task<IActionResult> Index()
        {
            var viewModel = new SystemAdminDashboardViewModel
            {
                TotalCompanies = await _context.InsuranceCompanies.CountAsync(),
                ActiveCompanies = await _context.InsuranceCompanies.CountAsync(c => c.IsActive),
                TotalUsers = await _userManager.Users.CountAsync(),
                CompanyManagers = await _userManager.GetUsersInRoleAsync("CompanyManager"),
                RecentCompanies = await _context.InsuranceCompanies
                    .OrderByDescending(c => c.Id)
                    .Take(5)
                    .ToListAsync()
            };

            return View(viewModel);
        }

        // Insurance Companies Management
        public async Task<IActionResult> Companies()
        {
            var companies = await _context.InsuranceCompanies
                .Include(c => c.Offices)
                .OrderByDescending(c => c.Id)
                .ToListAsync();

            return View(companies);
        }

        [HttpGet]
        public IActionResult CreateCompany()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateCompany(CreateCompanyViewModel model)
        {
            if (ModelState.IsValid)
            {
                var company = new InsuranceCompany
                {
                    Name = model.Name,
                    Description = model.Description,
                    PhoneNumber = model.PhoneNumber,
                    Email = model.Email,
                    Address = model.Address,
                    Website = model.Website,
                    IsActive = true
                };

                _context.InsuranceCompanies.Add(company);
                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "تم إنشاء الشركة بنجاح";
                return RedirectToAction("Companies");
            }

            return View(model);
        }

        [HttpGet]
        public async Task<IActionResult> EditCompany(int id)
        {
            var company = await _context.InsuranceCompanies.FindAsync(id);
            if (company == null)
            {
                return NotFound();
            }

            var viewModel = new EditCompanyViewModel
            {
                Id = company.Id,
                Name = company.Name,
                Description = company.Description,
                PhoneNumber = company.PhoneNumber,
                Email = company.Email,
                Address = company.Address,
                Website = company.Website,
                IsActive = company.IsActive
            };

            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditCompany(EditCompanyViewModel model)
        {
            if (ModelState.IsValid)
            {
                var company = await _context.InsuranceCompanies.FindAsync(model.Id);
                if (company == null)
                {
                    return NotFound();
                }

                company.Name = model.Name;
                company.Description = model.Description;
                company.PhoneNumber = model.PhoneNumber;
                company.Email = model.Email;
                company.Address = model.Address;
                company.Website = model.Website;
                company.IsActive = model.IsActive;

                await _context.SaveChangesAsync();

                TempData["SuccessMessage"] = "تم تحديث الشركة بنجاح";
                return RedirectToAction("Companies");
            }

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteCompany(int id)
        {
            var company = await _context.InsuranceCompanies.FindAsync(id);
            if (company == null)
            {
                return NotFound();
            }

            _context.InsuranceCompanies.Remove(company);
            await _context.SaveChangesAsync();

            TempData["SuccessMessage"] = "تم حذف الشركة بنجاح";
            return RedirectToAction("Companies");
        }

        // Users Management
        public async Task<IActionResult> Users()
        {
            var users = await _userManager.Users
                .Include(u => u.InsuranceCompany)
                .OrderByDescending(u => u.CreatedAt)
                .ToListAsync();

            var userViewModels = new List<UserManagementViewModel>();

            foreach (var user in users)
            {
                var roles = await _userManager.GetRolesAsync(user);
                userViewModels.Add(new UserManagementViewModel
                {
                    User = user,
                    Roles = roles.ToList()
                });
            }

            return View(userViewModels);
        }

        [HttpGet]
        public async Task<IActionResult> CreateUser()
        {
            ViewBag.Companies = await _context.InsuranceCompanies
                .Where(c => c.IsActive)
                .ToListAsync();

            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateUser(CreateUserViewModel model)
        {
            if (ModelState.IsValid)
            {
                var user = new ApplicationUser
                {
                    UserName = model.Email,
                    Email = model.Email,
                    FirstName = model.FirstName,
                    LastName = model.LastName,
                    NationalId = model.NationalId,
                    Address = model.Address,
                    DateOfBirth = model.DateOfBirth,
                    PhoneNumber = model.PhoneNumber,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true,
                    EmailConfirmed = true,
                    InsuranceCompanyId = model.Role == "CompanyManager" ? model.InsuranceCompanyId : null
                };

                var result = await _userManager.CreateAsync(user, model.Password);
                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(user, model.Role);
                    TempData["SuccessMessage"] = "تم إنشاء المستخدم بنجاح";
                    return RedirectToAction("Users");
                }

                foreach (var error in result.Errors)
                {
                    string arabicError = TranslateIdentityError(error.Code, error.Description);
                    ModelState.AddModelError(string.Empty, arabicError);
                }
            }

            ViewBag.Companies = await _context.InsuranceCompanies
                .Where(c => c.IsActive)
                .ToListAsync();

            return View(model);
        }

        [HttpGet]
        public async Task<IActionResult> EditUser(string id)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            var roles = await _userManager.GetRolesAsync(user);
            var viewModel = new EditUserViewModel
            {
                Id = user.Id,
                Email = user.Email!,
                FirstName = user.FirstName,
                LastName = user.LastName,
                NationalId = user.NationalId,
                Address = user.Address,
                DateOfBirth = user.DateOfBirth,
                PhoneNumber = user.PhoneNumber,
                IsActive = user.IsActive,
                Role = roles.FirstOrDefault() ?? "",
                InsuranceCompanyId = user.InsuranceCompanyId
            };

            ViewBag.Companies = await _context.InsuranceCompanies
                .Where(c => c.IsActive)
                .ToListAsync();

            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditUser(EditUserViewModel model)
        {
            if (ModelState.IsValid)
            {
                var user = await _userManager.FindByIdAsync(model.Id);
                if (user == null)
                {
                    return NotFound();
                }

                user.FirstName = model.FirstName;
                user.LastName = model.LastName;
                user.NationalId = model.NationalId;
                user.Address = model.Address;
                user.DateOfBirth = model.DateOfBirth;
                user.PhoneNumber = model.PhoneNumber;
                user.IsActive = model.IsActive;
                user.InsuranceCompanyId = model.Role == "CompanyManager" ? model.InsuranceCompanyId : null;

                var result = await _userManager.UpdateAsync(user);
                if (result.Succeeded)
                {
                    // Update user role
                    var currentRoles = await _userManager.GetRolesAsync(user);
                    await _userManager.RemoveFromRolesAsync(user, currentRoles);
                    await _userManager.AddToRoleAsync(user, model.Role);

                    TempData["SuccessMessage"] = "تم تحديث المستخدم بنجاح";
                    return RedirectToAction("Users");
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
            }

            ViewBag.Companies = await _context.InsuranceCompanies
                .Where(c => c.IsActive)
                .ToListAsync();

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteUser(string id)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            var result = await _userManager.DeleteAsync(user);
            if (result.Succeeded)
            {
                TempData["SuccessMessage"] = "تم حذف المستخدم بنجاح";
            }
            else
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف المستخدم";
            }

            return RedirectToAction("Users");
        }

        private string TranslateIdentityError(string code, string description)
        {
            return code switch
            {
                "DuplicateUserName" => "اسم المستخدم موجود مسبقاً",
                "DuplicateEmail" => "البريد الإلكتروني موجود مسبقاً",
                "InvalidEmail" => "البريد الإلكتروني غير صحيح",
                "PasswordTooShort" => "كلمة المرور قصيرة جداً، يجب أن تكون على الأقل 6 أحرف",
                "PasswordRequiresDigit" => "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل",
                "PasswordRequiresLower" => "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل",
                "PasswordRequiresUpper" => "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل",
                "PasswordRequiresNonAlphanumeric" => "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل",
                "InvalidUserName" => "اسم المستخدم غير صحيح",
                "UserAlreadyInRole" => "المستخدم موجود في هذا الدور مسبقاً",
                "UserNotInRole" => "المستخدم غير موجود في هذا الدور",
                "InvalidToken" => "الرمز المميز غير صحيح",
                "RecoveryCodeRedemptionFailed" => "فشل في استخدام رمز الاسترداد",
                "InvalidRoleName" => "اسم الدور غير صحيح",
                "DuplicateRoleName" => "اسم الدور موجود مسبقاً",
                _ => $"خطأ: {description}"
            };
        }
    }
}
