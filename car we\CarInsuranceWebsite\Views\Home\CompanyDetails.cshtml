@model CarInsuranceWebsite.ViewModels.CompanyDetailsViewModel
@{
    ViewData["Title"] = $"تفاصيل شركة {Model.Company.Name}";
}

<!-- Company Header -->
<section class="bg-light py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="d-flex align-items-center">
                    @if (!string.IsNullOrEmpty(Model.Company.LogoUrl))
                    {
                        <img src="@Model.Company.LogoUrl" alt="@Model.Company.Name" class="me-4 rounded" style="width: 100px; height: 100px; object-fit: cover;">
                    }
                    else
                    {
                        <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center me-4" style="width: 100px; height: 100px;">
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                    }
                    <div>
                        <h1 class="h2 mb-2">@Model.Company.Name</h1>
                        <p class="text-muted mb-1">@Model.Company.Description</p>
                        <div class="d-flex gap-3 text-muted">
                            <span><i class="fas fa-map-marker-alt me-1"></i> @Model.Company.Address</span>
                            <span><i class="fas fa-phone me-1"></i> @Model.Company.PhoneNumber</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="@Url.Action("Index")" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-right me-2"></i>العودة للشركات
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Company Info -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <h3 class="card-title mb-3">معلومات الشركة</h3>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-envelope text-primary me-3"></i>
                                    <div>
                                        <small class="text-muted d-block">البريد الإلكتروني</small>
                                        <span>@Model.Company.Email</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-globe text-primary me-3"></i>
                                    <div>
                                        <small class="text-muted d-block">الموقع الإلكتروني</small>
                                        @if (!string.IsNullOrEmpty(Model.Company.Website))
                                        {
                                            <a href="@Model.Company.Website" target="_blank" class="text-decoration-none">
                                                @Model.Company.Website
                                            </a>
                                        }
                                        else
                                        {
                                            <span class="text-muted">غير متوفر</span>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Offices Section -->
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h3 class="card-title mb-4">مكاتب الشركة (@Model.Offices.Count() مكتب)</h3>
                        
                        @if (Model.Offices.Any())
                        {
                            <div class="row g-4">
                                @foreach (var office in Model.Offices)
                                {
                                    <div class="col-lg-6">
                                        <div class="border rounded p-4 h-100">
                                            <h5 class="mb-3">@office.Name</h5>
                                            <div class="mb-3">
                                                <div class="d-flex align-items-start mb-2">
                                                    <i class="fas fa-map-marker-alt text-primary me-2 mt-1"></i>
                                                    <span>@office.Address</span>
                                                </div>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="fas fa-phone text-primary me-2"></i>
                                                    <span>@office.PhoneNumber</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-clock text-primary me-2"></i>
                                                    <span>@office.WorkingHours</span>
                                                </div>
                                            </div>
                                            
                                            @if (!string.IsNullOrEmpty(office.Description))
                                            {
                                                <p class="text-muted mb-3">@office.Description</p>
                                            }
                                            
                                            @if (office.Offers.Any())
                                            {
                                                <div class="mt-3">
                                                    <h6 class="mb-2">العروض المتاحة:</h6>
                                                    @foreach (var offer in office.Offers.Where(o => o.IsActive))
                                                    {
                                                        <div class="badge bg-light text-dark border me-2 mb-2">
                                                            @offer.Type.ToString()
                                                            <small class="text-muted">(@offer.MinPrice.ToString("C") - @offer.MaxPrice.ToString("C"))</small>
                                                        </div>
                                                    }
                                                </div>
                                            }
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-4">
                                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد مكاتب متاحة</h5>
                                <p class="text-muted">لا توجد مكاتب متاحة لهذه الشركة حالياً</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title mb-3">معلومات سريعة</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-building text-primary me-2"></i>
                                <strong>@Model.Offices.Count()</strong> مكتب متاح
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-clipboard-list text-primary me-2"></i>
                                <strong>@Model.Offices.SelectMany(o => o.Offers).Count(o => o.IsActive)</strong> عرض متاح
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                شركة معتمدة ومرخصة
                            </li>
                        </ul>
                        
                        <hr>
                        
                        <h6 class="mb-3">أنواع التأمين المتاحة</h6>
                        @{
                            var availableTypes = Model.Offices.SelectMany(o => o.Offers.Where(of => of.IsActive))
                                                              .Select(of => of.Type)
                                                              .Distinct()
                                                              .ToList();
                        }
                        
                        @if (availableTypes.Any())
                        {
                            @foreach (var type in availableTypes)
                            {
                                <div class="badge bg-primary me-2 mb-2">@type.ToString()</div>
                            }
                        }
                        else
                        {
                            <p class="text-muted small">لا توجد عروض متاحة حالياً</p>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
