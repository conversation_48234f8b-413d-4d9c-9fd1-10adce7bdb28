using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using CarInsuranceWebsite.Models;
using CarInsuranceWebsite.ViewModels;
using CarInsuranceWebsite.Data;

namespace CarInsuranceWebsite.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly ApplicationDbContext _context;

    public HomeController(ILogger<HomeController> logger, ApplicationDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    public async Task<IActionResult> Index(string? search, int page = 1)
    {
        var pageSize = 6;
        var query = _context.InsuranceCompanies
            .Where(c => c.IsActive)
            .Include(c => c.Offices.Where(o => o.IsActive));

        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(c => c.Name.Contains(search) || c.Description.Contains(search))
                        .Include(c => c.Offices.Where(o => o.IsActive));
        }

        var totalCount = await query.CountAsync();
        var companies = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        var viewModel = new CompanySearchViewModel
        {
            SearchTerm = search,
            Companies = companies,
            TotalCount = totalCount,
            PageNumber = page,
            PageSize = pageSize
        };

        return View(viewModel);
    }

    public async Task<IActionResult> CompanyDetails(int id)
    {
        var company = await _context.InsuranceCompanies
            .Include(c => c.Offices.Where(o => o.IsActive))
            .ThenInclude(o => o.Offers.Where(of => of.IsActive))
            .FirstOrDefaultAsync(c => c.Id == id && c.IsActive);

        if (company == null)
        {
            return NotFound();
        }

        var viewModel = new CompanyDetailsViewModel
        {
            Company = company,
            Offices = company.Offices.ToList()
        };

        return View(viewModel);
    }

    public IActionResult About()
    {
        return View();
    }

    public IActionResult Contact()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
