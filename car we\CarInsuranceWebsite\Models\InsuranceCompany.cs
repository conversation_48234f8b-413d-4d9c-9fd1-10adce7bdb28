using System.ComponentModel.DataAnnotations;

namespace CarInsuranceWebsite.Models
{
    public class InsuranceCompany
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [StringLength(20)]
        public string PhoneNumber { get; set; } = string.Empty;

        [StringLength(200)]
        public string Website { get; set; } = string.Empty;

        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        [StringLength(200)]
        public string Address { get; set; } = string.Empty;

        [StringLength(200)]
        public string LogoUrl { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual ICollection<CompanyOffice> Offices { get; set; } = new List<CompanyOffice>();
        public virtual ICollection<ApplicationUser> Managers { get; set; } = new List<ApplicationUser>();
    }
}
